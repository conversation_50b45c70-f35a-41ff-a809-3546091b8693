"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/layout/AppLayout";
import { Chat } from "@/components/chat";
import { Agent } from "@/components/agent/Agent";
import { useAppStore } from "@/lib/stores";
import { use } from "react";
import type { ChatPageProps } from "@/lib/types/page";

function ChatPageContent({ chatId }: { chatId: string }) {
  const { selectedModels, setSelectedModels } = useAppStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedMode, setSelectedMode] = useState<'chat' | 'agent'>('chat');

  // Get mode from URL params or default to 'chat'
  useEffect(() => {
    const mode = searchParams.get('mode') as 'chat' | 'agent';
    if (mode && (mode === 'chat' || mode === 'agent')) {
      setSelectedMode(mode);
    }
  }, [searchParams]);

  // Update URL when mode changes
  const handleModeChange = (mode: 'chat' | 'agent') => {
    setSelectedMode(mode);
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('mode', mode);
    router.replace(currentUrl.pathname + currentUrl.search);
  };

  const handleModelsChange = (models: string[]) => {
    setSelectedModels(models);
  };

  // Validate chatId format (basic check)
  if (!chatId || chatId.trim() === '') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Invalid Chat ID
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            The chat ID provided is not valid.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Mode Selector */}
      <div className="flex items-center justify-center gap-2 p-4 border-b bg-white dark:bg-gray-900 flex-shrink-0">
        <button
          onClick={() => handleModeChange('chat')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            selectedMode === 'chat' 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          Chat Mode
        </button>
        <button
          onClick={() => handleModeChange('agent')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            selectedMode === 'agent' 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          Agent Mode
        </button>
      </div>
      
      {/* Content Area */}
      <div className="flex-1 overflow-hidden h-full">
        {selectedMode === 'chat' ? (
          <Chat
            chatId={chatId}
            selectedModels={selectedModels}
            onModelsChange={handleModelsChange}
          />
        ) : (
          <Agent
            chatId={chatId}
            selectedModels={selectedModels}
          />
        )}
      </div>
    </div>
  );
}

export default function ChatPage({ params }: ChatPageProps) {
  const { chatId } = use(params);

  return (
    <AppLayout>
      <Suspense fallback={<div className="flex items-center justify-center h-full">Loading...</div>}>
        <ChatPageContent chatId={chatId} />
      </Suspense>
    </AppLayout>
  );
}