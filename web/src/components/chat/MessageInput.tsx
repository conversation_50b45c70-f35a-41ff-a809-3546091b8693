'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { Square, Paperclip, ChevronDown, Globe } from 'lucide-react';
import { Select } from 'antd';
import { useAppStore } from '@/lib/stores';
import Image from 'next/image';
import { LanguageMenu } from './LanguageMenu';








interface MessageInputProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  placeholder?: string;
  disabled?: boolean;
  transparentBackground?: boolean;
  selectedModels?: string[];
  onModelsChange?: (models: string[]) => void;
  files?: any[];
  onFilesChange?: (files: any[]) => void;
  className?: string;
  isGenerating?: boolean;
  onStop?: () => void;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  prompt,
  onPromptChange,
  onSubmit,
  placeholder = "Ask anything",
  disabled = false,
  transparentBackground = false,
  selectedModels = [],
  onModelsChange,
  files = [],
  onFilesChange,
  className,
  isGenerating = false,
  onStop
}) => {
  const { models } = useAppStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const languageButtonRef = useRef<HTMLButtonElement>(null);

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    if (onModelsChange) {
      onModelsChange([modelId]);
    }
  };

  // Handle language menu
  const handleLanguageButtonClick = () => {
    setIsLanguageMenuOpen(!isLanguageMenuOpen);
  };

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    // You can add additional logic here to handle language change
    console.log('Selected language:', languageCode);
  };

  const handleLanguageMenuClose = () => {
    setIsLanguageMenuOpen(false);
  };



  const handleSubmit = useCallback(async () => {
    // Prevent multiple simultaneous submissions
    if (isSubmitting || disabled || isGenerating) {
      return;
    }

    if (!prompt.trim() && files.length === 0) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if models are selected and not empty
    if (!selectedModels.length || selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        prompt: prompt.trim(),
        files,
        selectedModels
      });

      // Clear input after successful submit
      onPromptChange('');
      if (onFilesChange) {
        onFilesChange([]);
      }
    } catch (error) {
      console.error('Failed to submit message:', error);
      // Don't clear input on error so user can retry
    } finally {
      // Reset submitting state after a short delay
      setTimeout(() => {
        setIsSubmitting(false);
      }, 500);
    }
  }, [prompt, files, selectedModels, onSubmit, onPromptChange, onFilesChange, isSubmitting, disabled, isGenerating]);

  const handleFileUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = Array.from(e.target.files || []);
    
    if (uploadedFiles.length > 0) {
      const newFiles = uploadedFiles.map(file => ({
        id: Math.random().toString(36).substring(2, 11),
        name: file.name,
        type: file.type,
        size: file.size,
        file: file
      }));

      if (onFilesChange) {
        onFilesChange([...files, ...newFiles]);
      }
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [files, onFilesChange]);

  const removeFile = useCallback((fileId: string) => {
    if (onFilesChange) {
      onFilesChange(files.filter(file => file.id !== fileId));
    }
  }, [files, onFilesChange]);





  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  }, [handleSubmit]);

  return (
    <div className={cn(
      "w-full relative",
      transparentBackground ? "bg-transparent" : "bg-white dark:bg-gray-900",
      className
    )}>
      {/* File attachments display */}
      {files.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
          {files.map((file) => (
            <div
              key={file.id}
              className="flex items-center gap-2 bg-white dark:bg-gray-700 px-3 py-1 rounded-full text-sm"
            >
              <span className="truncate max-w-32">{file.name}</span>
              <button
                onClick={() => removeFile(file.id)}
                className="text-gray-500 hover:text-red-500 transition-colors"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Input container */}
      <div
        className="relative bg-white flex h-full w-full flex-col min-w-0 overflow-hidden rounded-[12px] border"
        style={{
          borderColor: '#E9E9EA',
          boxShadow: '0px 10px 10px -5px rgba(0, 0, 0, 0.02), 0px 20px 25px -5px rgba(0, 0, 0, 0.02)'
        }}
      >
        <div className="w-full">
          {/* Text input */}
          <textarea
            value={prompt}
            onChange={(e) => onPromptChange(e.target.value)}
            placeholder={placeholder}
            className="h-24 p-[16px] pb-0 w-full min-w-0 resize-none border-none outline-none bg-transparent overflow-hidden"
            onKeyDown={handleKeyDown}
            style={{ resize: 'none' }}
          />
        </div>
        <div className="flex items-center justify-between p-[16px] pt-[8px] min-w-0 overflow-hidden">
          {/* Left group */}
          <div className="flex items-center gap-[6px]">
            {/* File upload button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              className="w-[32px] h-[32px] rounded-[8px] bg-[#F7F8FA] hover:bg-[#EAEAF2]"
              title="Upload files"
            >
              <Paperclip className="h-4 w-4 text-[#2E2F41]" />
            </Button>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              accept="image/*,text/*,.pdf,.doc,.docx"
            />

            {/* Multi-language search button */}
            <Button
              ref={languageButtonRef}
              variant="ghost"
              className="h-[32px] px-[6px] rounded-[8px] bg-[#F7F8FA] hover:bg-[#EAEAF2] flex items-center gap-[4px]"
              title="Multi-language search"
              onClick={handleLanguageButtonClick}
              style={{display:'none'}}
            >
              <Globe className="h-4 w-4 text-[#2E2F41]" />
              <span className="text-sm text-[#2E2F41]">Multi-language search</span>
            </Button>
          </div>

          {/* Right group */}
          <div className="flex items-center gap-[8px]">
            {/* Model dropdown */}
            <div className="flex items-center">
              <Image
                src="/modelIcon.svg"
                alt="Model Icon"
                width={14}
                height={15}
                className="shrink-0"
              />
              <Select
                value={selectedModels[0] || undefined}
                onChange={handleModelSelect}
                placeholder="ChatGPT 4o"
                disabled={disabled}
                style={{ minWidth: 160 }}
                className="model-selector"
                suffixIcon={<ChevronDown className="h-4 w-4" />}
              >
              {models && models.length > 0 ? (
                models.map((model) => (
                  <Select.Option key={model.id} value={model.id}>
                    <span className="truncate">{model.name}</span>
                  </Select.Option>
                ))
              ) : (
                <Select.Option disabled value="">
                  No models available
                </Select.Option>
              )}
            </Select>
            </div>

            {/* Send/Stop button */}
            <Button
              onClick={isGenerating ? onStop : handleSubmit}
              disabled={!isGenerating && (disabled || isSubmitting || (!prompt.trim() && files.length === 0))}
              variant="ghost"
              size="icon"
              className="w-[32px] h-[32px] rounded-[8px] bg-[#625DEC] hover:bg-[#5A55E0] disabled:bg-gray-200"
              title={isGenerating ? "Stop generation" : isSubmitting ? "Sending..." : "Send message"}
            >
              {isGenerating ? (
                <Square className="w-4 h-4 text-white fill-current" />
              ) : (
                <Image
                  src="/Send.svg"
                  alt="Send"
                  width={16}
                  height={16}
                  className="text-white"
                />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Language Menu - positioned using fixed positioning */}
      <LanguageMenu
        isOpen={isLanguageMenuOpen}
        onClose={handleLanguageMenuClose}
        onLanguageSelect={handleLanguageSelect}
        selectedLanguage={selectedLanguage}
        buttonRef={languageButtonRef}
      />
    </div>
  );
};
